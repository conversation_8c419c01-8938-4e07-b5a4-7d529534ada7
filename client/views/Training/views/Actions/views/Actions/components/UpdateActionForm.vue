<template>
  <div class="wdt100 ht100 display-flex flex-direction-column" v-custom-loader.fullscreen="loading || syncing">
    <div class="display-flex wdt100 mt-16 create-action-form">
      <div class="wdt50 pt-32 px-20 ht100" :class="{'action-form': isWidgetVisible}">
        <el-form
          ref="actionForm"
          :model="actionForm"
          label-position="top"
          class="pb-40"
          :rules="validationRules"
        >
          <div class="mb-8 fw500">
            Action<span class="required-field">*</span>
          </div>
          <el-form-item prop="actionLabel" class="mb-26">
            <CustomInput
              isSelected
              class="mb-0"
              placeholder=""
              v-model="actionForm.actionLabel"
              :isReadOnly="isReadOnly"
              :maxLength="50"
              showWordLimit
            />
          </el-form-item>
          <div class="mb-8 fw500">
            Description<span class="required-field">*</span>
          </div>
          <el-form-item prop="actionDescription" class="mb-26">
            <CustomInput
              isSelected
              class="mb-0"
              type="textarea"
              placeholder=""
              :maxLength="1000"
              minRows="2"
              maxRows="10"
              v-model="actionForm.actionDescription"
              :isReadOnly="isReadOnly"
            />
          </el-form-item>

          <el-form-item prop="actionType" class="mb-26" label="Action type">
            <div class="radio-btn-cta py-8">
              <RadioButtonGroup
                :initialValue="actionForm.actionType"
                :radioOptions="[{ label: 'Recipe', value: 'RECIPE' , disabled:false },{ label: 'Workflow', value: 'WORKFLOW', disabled:true }]"
                :handleValueChange="(val)=>radioBtnHandler(val, index)"
              >
              </RadioButtonGroup>
            </div>
          </el-form-item>

          <div class="my-8 display-flex justify-content-between">
            <el-tabs v-model="selectedTab">
              <el-tab-pane :name="recipeEnvTabs.STAGING.value" :value="recipeEnvTabs.STAGING.value">
                <template #label>
                  <span class="display-flex gap-4">
                    {{ recipeEnvTabs.STAGING.label }}
                    <BaseIcon
                      v-if="stagingRecipeStatusError && isActiveDraftState"
                      name="WarningIcon"
                      :width="16"
                      :height="16"
                    />
                  </span>
                </template>
                &nbsp;
              </el-tab-pane>
              <el-tab-pane :name="recipeEnvTabs.PRODUCTION.value" :value="recipeEnvTabs.PRODUCTION.value">
                <template #label>
                  <span class="display-flex gap-4">
                    {{ recipeEnvTabs.PRODUCTION.label }}
                    <BaseIcon
                      v-if="productionRecipeStatusError && selectedState === ACTION_ENV_TABS.STAGING.value"
                      name="WarningIcon"
                      :width="16"
                      :height="16"
                    />
                  </span>
                </template>
              </el-tab-pane>
            </el-tabs>
            <el-checkbox
              v-if="selectedTab === recipeEnvTabs.PRODUCTION.value && isActiveDraftState"
              :disabled="isReadOnly"
              size="mini"
              v-model="syncStagingActive"
              @change="(val) => handleSyncStagingCheckbox(val)"
            >
              <span class="fs16 text-2c2c2c fw400">Same as Staging</span>
            </el-checkbox>
          </div>

          <div class="recipe-container mb-16">
            <div class="recipe-container-header" v-if="actionForm[selectedTab].selectedRecipe.recipeId">
              <p>Recipe</p>
              <div>
                <el-button type="text" @click="openDrawer" :disabled="isReadOnly">
                  <span class="btn-text mr-5">Switch</span>
                </el-button>

                <el-drawer
                  title="Integration recipes"
                  :visible.sync="drawer"
                  :before-close="handleClose"
                  destroyOnClose
                >
                  <RecipeList
                    :recipeId="
                      actionForm[selectedTab]?.selectedRecipe?.recipeId
                    "
                  />
                </el-drawer>
                <el-button
                  type="text"
                  icon="el-icon-refresh"
                  class="refresh-button"
                  @click="handleRefresh"
                  :disabled="isReadOnly"
                >
                  Refresh
                </el-button>
              </div>
            </div>
            <div v-else class="recipe-container-header">
              <p>Select recipe</p>
            </div>
            <div class="recipe-selection-box">
              <ReadEditButton
                v-if="!actionForm[selectedTab]?.selectedRecipe?.recipeId"
                label="Recipe Panel"
                :disabled="isReadOnly"
                :class="{'recipe-error': showRecipeError}"
              />
              <recipe-card
                v-else
                :id="actionForm[selectedTab].selectedRecipe.recipeId"
                :name="actionForm[selectedTab].selectedRecipe.name"
                :isActive="actionForm[selectedTab].selectedRecipe.isActive"
                :integrationLogo="
                  actionForm[selectedTab].selectedRecipe.integrationLogo
                "
                :trigger="actionForm[selectedTab].selectedRecipe.triggerType"
              />
            </div>
          </div>
          <p class="fs14 mb-8 text-e22c2c" v-if="showRecipeError">
            This is a required field
          </p>
          <Collapse :value="selectedParamAccordian">
            <StyledCollapse
              :isSubDescription="false"
              :title="inputParameterTitle"
              descriptionClass="ml-50"
              name="inputParameterTitle"
            >
              <el-form-item v-show="actionForm[selectedTab].selectedRecipe?.recipeId">
                <div
                  v-show="
                    actionForm[selectedTab].inputParameters?.mandatory?.length > 0
                  "
                >
                  <p class="my-24 fs16 fw600 text-6d6d6d lh-20">
                    Mandatory ({{
                      actionForm[selectedTab].inputParameters?.mandatory?.length
                    }})
                  </p>
                  <div
                    v-for="(field, index) in actionForm[selectedTab].inputParameters
                      .mandatory"
                    :key="field.name"
                    class="parameter"
                  >
                    <div class="display-flex gap-16 flex-direction-column">
                      <div class="px-12 py-8 display-flex align-items-center gap-4 parameter__label">
                        <BaseIcon :name="getIcon(field.type)" />
                        <p>
                          {{ field.label }}
                        </p>
                      </div>
                      <el-form-item prop="" class="mb-12" label="Allowed values">
                        <el-select
                          v-model="field.possibleValues"
                          class="wdt100"
                          multiple
                          filterable
                          allow-create
                          default-first-option
                          placeholder="Enter sample value"
                          :disabled="isReadOnly"
                        >
                          <el-option
                            v-for="item in field.possibleValues"
                            :key="item"
                            :label="item"
                            :value="item"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <div class="fw500">
                        Description<span class="required-field">*</span>
                      </div>
                      <el-form-item
                        :prop="
                          selectedTab +
                            '.inputParameters.mandatory.' +
                            index +
                            '.description'
                        "
                        :rules="validationRules.actionDescription"
                        class="mb-12"
                      >
                        <CustomInput
                          isSelected
                          class="mb-0"
                          placeholder="Enter input description"
                          v-model="field.description"
                          :maxLength="500"
                          :isReadOnly="isReadOnly"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </div>

                <div
                  v-show="
                    actionForm[selectedTab].inputParameters?.optional?.length > 0
                  "
                >
                  <p class="my-24 fs16 fw600 text-6d6d6d lh-20">
                    Optional ({{
                      actionForm[selectedTab].inputParameters?.optional?.length
                    }})
                  </p>
                  <div
                    v-for="(field, index) in actionForm[selectedTab].inputParameters
                      .optional"
                    :key="field.name"
                    class="parameter"
                  >
                    <div class="display-flex gap-16 flex-direction-column">
                      <div class="px-12 py-8 display-flex align-items-center gap-4 parameter__label">
                        <BaseIcon :name="getIcon(field.type)" />
                        <p>
                          {{ field.label }}
                        </p>
                      </div>
                      <el-form-item prop="" class="mb-12" label="Sample value">
                        <el-select
                          v-model="field.possibleValues"
                          class="wdt100"
                          multiple
                          filterable
                          allow-create
                          default-first-option
                          placeholder="Enter sample value"
                          :disabled="isReadOnly"
                        >
                          <el-option
                            v-for="item in field.possibleValues"
                            :key="item"
                            :label="item"
                            :value="item"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <div class="fw500">
                        Description<span class="required-field">*</span>
                      </div>
                      <el-form-item
                        class="mb-12"
                        :prop="
                          selectedTab +
                            '.inputParameters.optional.' +
                            index +
                            '.description'
                        "
                        :rules="validationRules.actionDescription"
                      >
                        <CustomInput
                          isSelected
                          class="mb-0"
                          placeholder="Enter input description"
                          v-model="field.description"
                          :maxLength="500"
                          :isReadOnly="isReadOnly"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </StyledCollapse>
            <StyledCollapse
              :isSubDescription="false"
              :title="outputParameterTitle"
              descriptionClass="ml-50"
              name="outputParameterTitle"
            >
              <el-form-item v-show="actionForm[selectedTab].selectedRecipe?.recipeId">
                <div
                  v-show="
                    actionForm[selectedTab].outputParameters?.mandatory?.length > 0
                  "
                >
                  <p class="my-24 fs16 fw600 text-6d6d6d lh-20">
                    Mandatory ({{
                      actionForm[selectedTab].outputParameters?.mandatory?.length
                    }})
                  </p>
                  <div
                    v-for="(field, index) in actionForm[selectedTab]
                      .outputParameters.mandatory"
                    :key="field.name"
                    class="parameter"
                  >
                    <div class="display-flex gap-16 flex-direction-column">
                      <div class="px-12 py-8 display-flex align-items-center gap-4 parameter__label">
                        <BaseIcon :name="getIcon(field.type)" />
                        <p>
                          {{ field.label }}
                        </p>
                      </div>
                      <div class="fw500">
                        Description<span class="required-field">*</span>
                      </div>
                      <el-form-item
                        :prop="
                          selectedTab +
                            '.outputParameters.mandatory.' +
                            index +
                            '.description'
                        "
                        :rules="validationRules.actionDescription"
                        class="mb-12"
                      >
                        <CustomInput
                          isSelected
                          class="mb-0"
                          placeholder="Enter output description"
                          v-model="field.description"
                          :maxLength="500"
                          :isReadOnly="isReadOnly"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </div>

                <div
                  v-show="
                    actionForm[selectedTab].outputParameters?.optional?.length > 0
                  "
                >
                  <p class="my-24 fs16 fw600 text-6d6d6d lh-20">
                    Optional ({{
                      actionForm[selectedTab].outputParameters?.optional?.length
                    }})
                  </p>
                  <div
                    v-for="(field, index) in actionForm[selectedTab]
                      .outputParameters.optional"
                    :key="field.name"
                    class="parameter"
                  >
                    <div class="display-flex gap-16 flex-direction-column">
                      <div class="px-12 py-8 display-flex align-items-center gap-4 parameter__label">
                        <BaseIcon :name="getIcon(field.type)" />
                        <p>
                          {{ field.name }}
                        </p>
                      </div>
                      <div class="fw500">
                        Description<span class="required-field">*</span>
                      </div>
                      <el-form-item
                        :prop="
                          selectedTab +
                            '.outputParameters.optional.' +
                            index +
                            '.description'
                        "
                        :rules="validationRules.actionDescription"
                        class="mb-12"
                      >
                        <CustomInput
                          isSelected
                          class="mb-0"
                          placeholder="Enter output description"
                          v-model="field.description"
                          :maxLength="500"
                          :isReadOnly="isReadOnly"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </StyledCollapse>
            <StyledCollapse
              :isSubDescription="false"
              title="Custom fields(optional)"
              descriptionClass="ml-50"
              name="customFieldParameterTitle"
            >
              <el-form-item v-show="actionForm[selectedTab].selectedRecipe?.recipeId">
                <div>
                  <div class="custom-field-banner">
                    <InlineBanner title="Manage response fields in 'Settings>AI Agent'." :showLearnMore="false" />
                  </div>
                  <div class="field-container">
                    <div>
                      <div class="field-container--title">
                        Fields
                      </div>
                      <div class="field-container--subtitle">
                        Configure custom field that will be added to the conversation as tags in your desk.
                      </div>
                    </div>
                    <div class="field-container--add-button" @click="addCustomField" :disabled="isReadOnly">
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 19C11.7167 19 11.4793 18.904 11.288 18.712C11.096 18.5207 11 18.2833 11 18V13H6C5.71667 13 5.479 12.904 5.287 12.712C5.09567 12.5207 5 12.2833 5 12C5 11.7167 5.09567 11.479 5.287 11.287C5.479 11.0957 5.71667 11 6 11H11V6C11 5.71667 11.096 5.479 11.288 5.287C11.4793 5.09567 11.7167 5 12 5C12.2833 5 12.521 5.09567 12.713 5.287C12.9043 5.479 13 5.71667 13 6V11H18C18.2833 11 18.5207 11.0957 18.712 11.287C18.904 11.479 19 11.7167 19 12C19 12.2833 18.904 12.5207 18.712 12.712C18.5207 12.904 18.2833 13 18 13H13V18C13 18.2833 12.9043 18.5207 12.713 18.712C12.521 18.904 12.2833 19 12 19Z"
                          fill="white"
                        />
                      </svg>
                    </div>
                  </div>
                  <div
                    v-for="(field, index) in actionForm[selectedTab].customFields"
                    :key="field.id || index"
                    class="parameter"
                  >
                    <div class="custom-field-item">
                      <div class="close-button" @click="removeCustomField(index)">
                        <BaseIcon name="CloseIcon" :width="14" :height="14" />
                      </div>
                      <div class="field">
                        <div class="field-label">
                          Field name
                        </div>
                        <el-select
                          ref="customSelect"
                          v-model="field.selectedField"
                          default-first-option
                          placeholder="Select field name"
                          size="large"
                          class="w-full"
                          value-key="id"
                          @change="(value) => handleCustomFieldChange(value, index)"
                        >
                          <el-input
                            v-model="searchValue"
                            class="p-16 w-full"
                            placeholder="Search"
                            prefix-icon="el-icon-search"
                          />
                          <el-option-group
                            v-for="group in dispalyOptions"
                            :key="group.type"
                            :label="getGroupLabel(group.type)"
                          >
                            <el-option
                              v-for="customField in group.customfields"
                              :key="customField.id"
                              :label="getLabel(customField)"
                              :value="customField"
                              class="options"
                              :disabled="customField.isSelected"
                              :class="{
                                'no-match-found': isFieldNotPresent(dispalyOptions),
                                'custom-field-block-selected': customField.isSelected
                              }"
                              data-search-exclude="true"
                            />
                            <div
                              v-if="
                                !isFieldNotPresent(dispalyOptions) && checkForBoolean(group)
                              "
                              class="separator"
                            />
                          </el-option-group>
                        </el-select>
                        <div v-if="field.selectedField && field.selectedField.type" class="selectedFieldType">
                          Field type: {{ getFieldTypeLabel(field.selectedField.type) }}
                        </div>
                      </div>

                      <!-- Field Value Section -->
                      <div v-if="field.selectedField && field.selectedField.type" class="field">
                        <div class="field-label">
                          Field value
                        </div>

                        <!-- TEXT type: Simple text input -->
                        <el-input
                          v-if="field.selectedField.type === 'TEXT'"
                          v-model="field.value"
                          placeholder="Enter text"
                          size="large"
                          class="w-full"
                          :disabled="isReadOnly"
                        />

                        <!-- MULTI-SELECT type: Multi-select dropdown -->
                        <el-select
                          v-else-if="field.selectedField.type === 'MULTI-SELECT'"
                          v-model="field.values"
                          multiple
                          placeholder="Select values"
                          size="large"
                          class="w-full"
                          :disabled="isReadOnly"
                        >
                          <el-option
                            v-for="option in field.selectedField.values"
                            :key="option"
                            :label="option"
                            :value="option"
                          />
                        </el-select>

                        <!-- BOOLEAN type: Single select dropdown with true/false -->
                        <el-select
                          v-else-if="field.selectedField.type === 'BOOLEAN'"
                          v-model="field.value"
                          placeholder="Select value"
                          size="large"
                          class="w-full"
                          :disabled="isReadOnly"
                        >
                          <el-option
                            v-for="option in field.selectedField.values"
                            :key="option"
                            :label="option"
                            :value="option"
                          />
                        </el-select>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </StyledCollapse>
          </Collapse>
        </el-form>
      </div>
      <div class="wdt50 display-flex pos-rel justify-content-center test-widget-container" ref="widgetContainer">
        <div class="static-chat-widget" :style="{width: widgetWidth + 'px'}">
          <TestWidget
            v-if="canShowWidget"
            :key="selectedState"
            :isStagingActive="selectedState === ACTION_ENV_TABS.STAGING.value"
            :isProductionActive="selectedState === ACTION_ENV_TABS.PRODUCTION.value"
            :toggleIsWidgetVisible="toggleIsWidgetVisible"
            showVisitorInfoButton
            passCustomAttributes
          />
        </div>
      </div>
      <ConfirmationModal
        v-loading="loading"
        title=""
        :showDialog="confirmationModalData.isOpen"
        className="sync-dialog"
      >
        <div slot="body" class="pb-0">
          <div class="mt-16">
            <span class="pos-abs close-btn" @click="confirmationModalData.onCancel">
              <BaseIcon name="CloseIcon" />
            </span>
            <div class="modal-title fw600 color-black fs24 pt-24 px-32 pb-20">
              {{ confirmationModalData.title }}
            </div>
            <p class="fs16 px-32 pt-16 pb-16">
              {{ confirmationModalData.desc }}
              <br />
              <br />
              <span class="fw600">{{ confirmationModalData.commentMessage }}</span>
            </p>
            <CustomInput
              v-if="confirmationModalData.showCommentBox"
              type="textarea"
              placeholder=""
              class="px-32 pb-32"
              @input="(val) => handleCommentChange(val)"
              :value="confirmationModalData.comment"
              minRows="1.75"
            />
          </div>
        </div>
        <div slot="footer">
          <div class="justify-content-between">
            <el-button class="el-button--without-border fw600" @click="confirmationModalData.onCancel">
              Cancel
            </el-button>
            <el-button type="primary" @click="confirmationModalData.onConfirm">
              {{ confirmationModalData.successBtnText }}
            </el-button>
          </div>
        </div>
      </ConfirmationModal>
    </div>

    <div class="sticky-footer">
      <div v-if="!isActionArchived" class="display-flex justify-content-between">
        <div class="display-flex">
          <div>
            <p class="text-6b6d7c mt-0">
              Character count
            </p>
            <p class="mt-5" :class="{'text-e22c2c': (currentTabCharacterCount > MAX_CHARACTER_LIMIT) }">
              {{ currentTabCharacterCount }} / {{ MAX_CHARACTER_LIMIT }}
            </p>
          </div>
          <TooltipWithInfoIcon
            iconContainerClass="ml-12"
            iconWidth="16"
            iconHeight="16"
            popperClass="wdtp300 fs14"
            placement="top-start"
            fillColor="var(--blue-medium)"
            effect="light"
            tooltipContentClass="p-10 text-2c2c2c"
            tooltipText="Use up to 1000 characters in total for action, descriptions, input parameters and output parameters."
          >
          </TooltipWithInfoIcon>
        </div>
        <div>
          <el-button
            @click="handleDeleteDraftDialog"
            :disabled="!isActionDraftPresent"
            class="py-8 px-12"
            v-if="isActiveDraftState && $userHasAccess(actionsPolicy._resource, actionsPolicy.DeleteAction)"
          >
            <BaseIcon name="DeleteIcon" />
          </el-button>
          <el-button
            class="fs16 fw600 ml-16"
            @click="handleMoveStateToDraft"
            v-show="(selectedState === ACTION_ENV_TABS.STAGING.value || selectedState === ACTION_ENV_TABS.PRODUCTION.value || selectedState === ACTION_ENV_TABS.PREVIOUS_VERSION.value) && $userHasAccess(actionsPolicy._resource, actionsPolicy.UpdateAction)"
          >
            Edit as Draft
          </el-button>
          <el-button
            class="fs16 fw600 ml-16"
            :disabled="!isStagingSyncEnabled"
            @click="moveActionToState('MOVE_TO_STAGING','STAGING')"
            type="primary"
            v-show="isActiveDraftState && $userHasAccess(actionsPolicy._resource, actionsPolicy.DeployStagingAction)"
          >
            Move to Staging
          </el-button>
          <el-button
            class="fs16 fw600 ml-16"
            :disabled="!isProductionSyncDisabled"
            @click="moveActionToState('MOVE_TO_PRODUCTION','PRODUCTION')"
            type="primary"
            v-show="selectedState === ACTION_ENV_TABS.STAGING.value && $userHasAccess(actionsPolicy._resource, actionsPolicy.DeployProductionAction)"
          >
            Sync to Production
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import constantStrings from '@/services/constants/constant-strings'
import { actionsPolicy } from '@/services/constants/policy-actions'
import { debounce, deepClone, isValueEmpty, isValuesEqual, objectPick } from '@/services/utils'
import { cloneDeep } from 'lodash'
import InlineBanner from '../../../../../../../components/InlineBanner'
import TestWidget from '../../../../../../PromptManager/views/Design/components/TestWidget'
import { ACTION_ENV_TABS, ACTION_FORM_VALIDATION_RULES, ACTION_MESSAGES, CustomFieldNodeType, MAX_CHARACTER_LIMIT, recipeEnvTabs } from '../../../constants'
import ReadEditButton from './ReadEditButton'
import RecipeCard from './RecipeCard'
import RecipeList from './RecipeList'

export default {
  name: 'UpdateActionForm',
  components: {
    TestWidget,
    ReadEditButton,
    RecipeCard,
    RecipeList,
    InlineBanner
  },
  data () {
    return {
      actionForm: {
        actionDescription: '',
        actionLabel: '',
        actionType: 'RECIPE',
        staging: {
          selectedRecipe: {},
          inputParameters: [],
          outputParameters: [],
          customFields: []
        },
        production: {
          selectedRecipe: {},
          inputParameters: [],
          outputParameters: [],
          customFields: []
        }
      },
      selectedTab: 'staging',
      initialFormState: {},
      loading: false,
      syncing: false,
      drawer: false,
      saving: false,
      selectedParamAccordian: [],
      needsSave: false,
      syncInitialState: true,
      ACTION_ENV_TABS,
      recipeEnvTabs,
      confirmationModalData: {
        isOpen: false,
        title: '',
        comment: '',
        desc: '',
        showCommentBox: false,
        commentMessage: '',
        successBtnText: 'Move',
        onConfirm: () => {},
        onCancel: () => {}
      },
      observer: null,
      widgetWidth: '',
      syncStagingActive: true,
      stagingRecipeError: false,
      productionRecipeError: false,
      stagingRecipeStatusError: false,
      productionRecipeStatusError: false,
      MAX_CHARACTER_LIMIT,
      searchValue: '',
      options: null,
      filteredOptions: [],
      selectedValue: null
    }
  },
  provide () {
    return {
      handleRecipeSelect: this.handleRecipeSelect,
      closeDrawer: this.handleClose
    }
  },
  props: {
    action: {
      type: Object
    },
    actionVersions: {
      type: Array,
      default: []
    },
    selectedState: {
      type: String,
      default: ""
    },
    actionId: {
      type: String,
      default: '',
      required: true
    },
    isActionArchived: {
      type: Boolean,
      default: false
    },
    showObservationPanel: {
      type: Boolean,
      default: false
    },
    toggleObservationPanel: {
      type: Function,
      default: () => {}
    },
    isWidgetVisible: {
      type: Boolean,
      default: false
    }
  },
  inject: ['updateDraftAction', 'reloadVersions', 'handleEditStateAsDraft', 'fetchVersionHistory', 'handleRestoreVersion', 'toggleIsWidgetVisible', 'updateObservationPanelWidth'],
  methods: {
    createGroups () {
      if (!this.customFields || !Array.isArray(this.customFields)) {
        this.options = []
        return
      }
      const fields = cloneDeep(this.customFields)
      fields.sort((a, b) => {
        const typeOrder = { TEXT: 1, 'MULTI-SELECT': 2, BOOLEAN: 3 }
        return typeOrder[a.type] - typeOrder[b.type]
      })
      const groupedOptions = {}
      fields.forEach((option) => {
        if (!groupedOptions[option.type]) {
          groupedOptions[option.type] = {
            type: option.type,
            customfields: []
          }
        }
        groupedOptions[option.type].customfields.push(option)
      })
      this.options = Object.values(groupedOptions)
    },
    getGroupLabel (type) {
      switch (type) {
        case CustomFieldNodeType.MULTISELECT:
          return 'Multi Select (Multiple values can be defined)'
        case CustomFieldNodeType.BOOLEAN:
          return 'Boolean (Max 2 values can be defined)'
        case CustomFieldNodeType.TEXT:
          return 'Text (Input free text)'
        default:
          return ''
      }
    },
    generateParameterTitle (paramType, paramName) {
      const selectedTab = this.selectedTab
      const parameters = this.actionForm[selectedTab][paramType]
      const mandatoryCount = parameters?.mandatory?.length || 0
      const optionalCount = parameters?.optional?.length || 0
      const total = mandatoryCount + optionalCount
      return `${paramName} (${total})`
    },
    radioBtnHandler (val, index) {
      this.actionForm.actionType = val
    },
    handleDescriptionInput (value) {
      this.actionForm.actionDescription = value
    },
    prepareMetaInfo (stage) {
      if (isValueEmpty(this.actionForm[stage].selectedRecipe)) return {}
      const formConfig = this.actionForm[stage]
      return {
        recipeId: formConfig.selectedRecipe.recipeId,
        headers: {
          'Content-Type': 'application/json'
        },
        inputParameters: [
          ...formConfig.inputParameters?.mandatory,
          ...formConfig.inputParameters?.optional
        ].map((parameter) => ({
          name: parameter?.name,
          label: parameter?.label,
          type: this.getDataType(parameter?.type),
          description: parameter?.description,
          optional: parameter?.optional,
          default_value: '',
          possible_values: parameter?.possibleValues
        })),
        outputParameters: [
          ...formConfig.outputParameters?.mandatory,
          ...formConfig.outputParameters?.optional
        ].map((parameter) => ({
          name: parameter?.name,
          label: parameter?.label,
          type: this.getDataType(parameter?.type),
          description: parameter?.description,
          optional: parameter?.optional,
          default_value: '',
          possible_values: parameter?.possibleValues
        })),
        customFields: formConfig.customFields
          .filter(field => field.selectedField && field.selectedField.id)
          .map((field) => ({
            id: field.selectedField.id,
            name: field.selectedField.name,
            type: field.selectedField.type,
            values: field.selectedField.type === 'MULTI-SELECT'
              ? field.values
              : field.selectedField.type === 'BOOLEAN'
                ? [field.value]
                : field.selectedField.type === 'TEXT' ? [field.value] : [],
            active: field.selectedField.active
          }))
      }
    },
    async handleDraftSave () {
      this.needsSave = false
      if (this.isActiveDraftState && !this.isReadOnly && this.$userHasAccess(actionsPolicy._resource, actionsPolicy.UpdateAction)) {
        try {
          this.saving = true
          const actionPayload = {
            actionLabel: this.actionForm?.actionLabel?.trim(),
            actionName: this.actionForm?.actionLabel
              ?.trim()
              .toLowerCase()
              .split(' ')
              .join('_'),
            actionType: 'RECIPE',
            actionDescription: this.actionForm.actionDescription,
            actionId: this.actionId,
            stage: ACTION_ENV_TABS.DRAFT.value,
            sandboxActionMetadata: this.prepareMetaInfo('staging'),
            liveActionMetadata: this.prepareMetaInfo('production')
          }
          const actionResponse = await this.$store.dispatch('training/updateDraftAction', {
            id: this.actionId,
            data: actionPayload
          })
          this.updateDraftAction(actionResponse)
        } catch (e) {
          this.$store.dispatch('error', e, { root: true })
        } finally {
          this.saving = false
          if (this.needsSave) {
            this.$nextTick(() => {
              this.handleDraftSave()
            })
          }
        }
      }
    },
    queueSave () {
      if (this.needsSave && !this.saving && !this.syncing) {
        this.handleDraftSave()
      }
    },
    getDataType (heimdallType) {
      switch (heimdallType) {
        case 'TEXT':
          return 'str'

        case 'LIST':
          return 'list'

        case 'BOOLEAN':
          return 'bool'

        case 'INTEGER':
          return 'int'

        case 'OBJECT':
          return 'dict'

        case 'NUMBER':
        case 'FLOAT':
          return 'float'

        default:
          return heimdallType
      }
    },
    parseMetadataBeforeSync (metaData) {
      const flattenedInputMetadata = metaData?.inputParameters?.map(
        ({ possible_values, ...rest }) => ({
          ...rest,
          possibleValues: possible_values
        })
      )

      const flattenedOutputMetadata = metaData?.outputParameters?.map(
        ({ possible_values, ...rest }) => ({
          ...rest,
          possibleValues: possible_values
        })
      )

      const inputParameters = {
        optional: flattenedInputMetadata?.filter(({ optional }) => optional),
        mandatory: flattenedInputMetadata?.filter(({ optional }) => !optional)
      }
      const outputParameters = {
        optional: flattenedOutputMetadata?.filter(({ optional }) => optional),
        mandatory: flattenedOutputMetadata?.filter(({ optional }) => !optional)
      }
      return { inputParameters, outputParameters }
    },
    async syncActionFromApi () {
      console.log("syncing")
      this.syncing = true
      const {
        actionLabel = '',
        actionDescription = '',
        sandboxActionMetadata = {},
        liveActionMetadata = {}
      } = this.action
      this.$set(this.actionForm, 'actionLabel', actionLabel)
      this.$set(this.actionForm, 'actionDescription', actionDescription)
      if (!isValueEmpty(sandboxActionMetadata)) {
        const { inputParameters, outputParameters } =
            this.parseMetadataBeforeSync(sandboxActionMetadata)
        await this.getRecipe(sandboxActionMetadata.recipeId, 'staging')
        this.$set(this.actionForm.staging, 'inputParameters', inputParameters)
        this.$set(this.actionForm.staging, 'outputParameters', outputParameters)
        if (isValueEmpty(sandboxActionMetadata.customFields)) {
          this.$set(this.actionForm.staging, 'customFields', [])
        } else {
          const transformedCustomFields = this.transformCustomFieldsFromApi(sandboxActionMetadata.customFields)
          this.$set(this.actionForm.staging, 'customFields', transformedCustomFields)
          // Mark selected fields as selected in options
          this.markSelectedCustomFields(transformedCustomFields)
        }
      }

      if (!isValueEmpty(liveActionMetadata)) {
        const { inputParameters, outputParameters } =
            this.parseMetadataBeforeSync(liveActionMetadata)

        await this.getRecipe(liveActionMetadata.recipeId, 'production')
        this.$set(
          this.actionForm.production,
          'inputParameters',
          inputParameters
        )
        this.$set(
          this.actionForm.production,
          'outputParameters',
          outputParameters
        )
        if (isValueEmpty(liveActionMetadata.customFields)) {
          this.$set(this.actionForm.production, 'customFields', [])
        } else {
          const transformedCustomFields = this.transformCustomFieldsFromApi(liveActionMetadata.customFields)
          this.$set(this.actionForm.production, 'customFields', transformedCustomFields)
          // Mark selected fields as selected in options
          this.markSelectedCustomFields(transformedCustomFields)
        }
      }
      if (this.syncInitialState) {
        this.initialFormState = deepClone(this.actionForm)
      }
      this.syncInitialState = true
      this.syncing = false
    },
    async getRecipe (id, stage) {
      try {
        this.loading = true
        const recipe = await this.$store.dispatch(
          'heimdall/fetchRecipeById',
          id
        )
        this.$set(this.actionForm[stage], 'selectedRecipe', recipe)
        return recipe
      } catch (e) {
        this.$store.dispatch('error', e)
      } finally {
        this.loading = false
      }
    },
    getTransformedVariables (variables = []) {
      if (!variables?.length) return []
      return variables?.map(
        ({ variableKey = '', variableName = '', dataType = '', required }) => ({
          name: variableKey,
          label: variableName,
          type: dataType,
          possibleValues: [],
          description: '',
          optional: !required
        })
      )
    },
    handleRecipeSelect (recipe) {
      const recipeProperties = JSON.parse(recipe.properties)
      const inputVariables = this.getTransformedVariables(
        recipeProperties?.inputVariables
      )
      const outputVariables = this.getTransformedVariables(
        recipeProperties?.outputVariables
      )
      const inputParameters = {
        mandatory: inputVariables?.filter(({ optional }) => !optional) || [],
        optional: inputVariables?.filter(({ optional }) => optional) || []
      }
      const outputParameters = {
        mandatory: outputVariables?.filter(({ optional }) => !optional),
        optional: outputVariables?.filter(({ optional }) => optional)
      }
      this.$set(this.actionForm[this.selectedTab], 'selectedRecipe', recipe)
      this.$set(
        this.actionForm[this.selectedTab],
        'inputParameters',
        inputParameters
      )
      this.$set(
        this.actionForm[this.selectedTab],
        'outputParameters',
        outputParameters
      )
    },
    getIcon (dataType) {
      switch (dataType) {
        case 'TEXT':
        case 'str':
          return 'StringDataTypeIcon'
        case 'NUMBER':
        case 'num':
        case 'FLOAT':
        case 'float':
          return 'NumberDataTypeIcon'
        case 'INTEGER':
        case 'int':
          return 'IntegerDataTypeIcon'
        case 'BOOLEAN':
        case 'bool':
          return 'BooleanDataTypeIcon'
        case 'LIST':
        case 'list':
          return 'ListDataTypeIcon'
        case 'OBJECT':
        case 'dict':
        case 'obj':
          return 'ObjectDataTypeIcon'
        default:
          return 'StringDataTypeIcon'
      }
    },
    async moveActionToState (type, stage) {
      try {
        await this.$refs['actionForm'].validate()
      } catch (_err) {
      }
      if (!this.isFormValidated(stage)) {
        const error = {}
        error.customMessage = `Move to ${ACTION_ENV_TABS[stage].label} failed. Fill all ${ACTION_ENV_TABS[stage].label} data`
        this.$store.dispatch('failure', error, { root: true })
        return
      }
      if (!this.isCharacterCountValidated(stage)) {
        const error = {}
        error.customMessage = `Limit exceeded. Use up to ${MAX_CHARACTER_LIMIT} characters for action, descriptions, input and output.`
        this.$store.dispatch('failure', error, { root: true })
        return
      }
      if (this.isRecipeStatusInactive(stage)) {
        const error = {}
        error.customMessage = `Move to ${ACTION_ENV_TABS[stage].label} failed. Make sure recipe is active for ${ACTION_ENV_TABS[stage].label}.`
        this.$store.dispatch('failure', error, { root: true })
        return
      }
      const desc = this.getActionDescription(stage)
      this.confirmationModalData = {
        title: stage === ACTION_ENV_TABS.STAGING.value ? `Move to ${constantStrings.en.STAGING}?` : `Sync to ${constantStrings.en.PRODUCTION}?`,
        desc,
        comment: '',
        isOpen: true,
        showCommentBox: true,
        commentMessage: stage === ACTION_ENV_TABS.PRODUCTION.value && this.currentProductionVersion ? 'Reason for replacement (optional)' : 'Add comments to this version (optional)',
        successBtnText: stage === ACTION_ENV_TABS.PRODUCTION.value ? 'Sync' : 'Move',
        onConfirm: () => this.upgradeActionState(type, stage, this.confirmationModalData.comment),
        onCancel: () => {
          this.confirmationModalData.isOpen = false
        }
      }
    },
    async upgradeActionState (type, stage, comment = '') {
      try {
        this.confirmationModalData.isOpen = false
        this.loading = true
        let activityText
        if (stage === ACTION_ENV_TABS.STAGING.value) {
          activityText = 'Moved from Draft to Staging'
        } else {
          activityText = 'Synced to Production'
        }
        await this.$store.dispatch(
          'training/promoteActionState',
          {
            id: this.actionId,
            type,
            comment,
            activityText
          }
        )
        await this.reloadVersions(stage)
        this.fetchVersionHistory()
      } catch (e) {
      } finally {
        this.loading = false
      }
    },
    handleMoveStateToDraft () {
      if (this.selectedState === ACTION_ENV_TABS.PREVIOUS_VERSION.value) {
        this.handleRestoreVersion()
        return
      }
      this.syncInitialState = false
      const actionData = this.actionVersions.find(version => version.stage === this.selectedState)
      const formKeys = ['actionLabel', 'actionDescription', 'actionType', 'sandboxActionMetadata', 'liveActionMetadata']
      const formData = deepClone(objectPick(actionData, formKeys))
      this.handleEditStateAsDraft(formData)
    },
    handleCommentChange (value) {
      this.confirmationModalData.comment = value
    },
    handleDeleteDraftDialog () {
      this.confirmationModalData = {
        title: 'Delete draft ?',
        desc: "This will permanently delete the draft you're working on.",
        isOpen: true,
        showCommentBox: false,
        successBtnText: 'Delete',
        onConfirm: () => this.handleDeleteDraft(),
        onCancel: () => {
          this.confirmationModalData.isOpen = false
        }
      }
    },

    async handleDeleteDraft () {
      try {
        this.loading = true
        await this.$store.dispatch(
          'training/deleteDraftAction',
          this.actionId
        )
        this.initialFormState = {}
        this.resetActionForm()
        await this.reloadVersions()
      } catch (e) {
        this.$store.dispatch('error', e, { root: true })
      } finally {
        this.loading = false
        this.confirmationModalData.isOpen = false
      }
    },

    resetActionForm () {
      this.actionForm = {
        actionDescription: '',
        actionLabel: '',
        actionType: 'RECIPE',
        staging: {
          selectedRecipe: {},
          inputParameters: [],
          outputParameters: [],
          customFields: []
        },
        production: {
          selectedRecipe: {},
          inputParameters: [],
          outputParameters: [],
          customFields: []
        }
      }
    },
    openDrawer () {
      this.drawer = true
    },
    closeDrawer () {
      this.drawer = false
    },
    handleClose () {
      this.drawer = false
    },
    mergeParameters (existingParams, newParams) {
      const filteredExistingParams = existingParams.filter((existingParam) =>
        newParams.some((newParam) => newParam.name === existingParam.name)
      )
      const mergedParams = [...filteredExistingParams]
      newParams.forEach((newParam) => {
        const index = mergedParams.findIndex((param) => param.name === newParam.name)
        if (index == -1) {
          mergedParams.push(newParam)
        }
      })
      return mergedParams
    },

    async handleRefresh () {
      const recipe = await this.getRecipe(
        this.actionForm[this.selectedTab]?.selectedRecipe?.recipeId,
        this.selectedTab
      )
      const recipeProperties = JSON.parse(this.actionForm[this.selectedTab].selectedRecipe.properties)
      const inputVariables = this.getTransformedVariables(
        recipeProperties?.inputVariables
      )
      const outputVariables = this.getTransformedVariables(
        recipeProperties?.outputVariables
      )
      const inputParameters = {
        mandatory: inputVariables?.filter(({ optional }) => !optional) || [],
        optional: inputVariables?.filter(({ optional }) => optional) || []
      }

      const outputParameters = {
        mandatory: outputVariables?.filter(({ optional }) => !optional),
        optional: outputVariables?.filter(({ optional }) => optional)
      }
      const updatedInputParams = {
        mandatory: this.mergeParameters(this.actionForm[this.selectedTab].inputParameters.mandatory, inputParameters.mandatory),
        optional: this.mergeParameters(this.actionForm[this.selectedTab].inputParameters.optional, inputParameters.optional)
      }

      const updatedOutputParams = {
        mandatory: this.mergeParameters(this.actionForm[this.selectedTab].outputParameters.mandatory, outputParameters.mandatory),
        optional: this.mergeParameters(this.actionForm[this.selectedTab].outputParameters.optional, outputParameters.optional)
      }
      if (!isValuesEqual(inputParameters, updatedInputParams) && !isValuesEqual(outputParameters, updatedOutputParams)) {
        this.selectedParamAccordian = ['inputParameterTitle', 'outputParameterTitle', 'customFieldParameterTitle']
      } else if (!isValuesEqual(inputParameters, updatedInputParams)) {
        this.selectedParamAccordian = ['inputParameterTitle']
      } else if (!isValuesEqual(outputParameters, updatedOutputParams)) {
        this.selectedParamAccordian = ['outputParameterTitle']
      }
      this.actionForm[this.selectedTab].inputParameters = updatedInputParams
      this.actionForm[this.selectedTab].outputParameters = updatedOutputParams
    },
    handleWidgetWidth () {
      const ele = document.querySelector('.test-widget-container')
      const width = ele.clientWidth
      this.widgetWidth = width
      this.updateObservationPanelWidth(width)
    },
    handleSyncStagingCheckbox (val) {
      if (val) {
        this.confirmationModalData = {
          title: 'Same as Staging information',
          desc: 'Enabling this will replace all production data with staging data.',
          comment: '',
          isOpen: true,
          showCommentBox: false,
          commentMessage: '',
          successBtnText: 'Enable',
          onConfirm: () => {
            this.syncStagingActive = true
            this.actionForm.production = deepClone(this.actionForm.staging)
            this.confirmationModalData.isOpen = false
          },
          onCancel: () => {
            this.confirmationModalData.isOpen = false
            this.syncStagingActive = false
          }
        }
      } else {
        this.$store.dispatch('info', {message: 'Unchecked auto-sync checkbox from Staging.'}, { root: true })
      }
    },
    isFormValidated (stage) {
      this.stagingRecipeError = false
      this.productionRecipeError = false
      this.stagingRecipeStatusError = false
      this.productionRecipeStatusError = false
      if (stage === ACTION_ENV_TABS.STAGING.value) {
        const {actionLabel, actionDescription, staging} = this.actionForm
        if (!staging.selectedRecipe?.recipeId) {
          this.stagingRecipeError = true
          return false
        }
        if (actionLabel.length < 2 || actionLabel.length > 50 || actionDescription.length < 2 || !this.validateInputAndOutputParameters(staging)) {
          return false
        }
        return true
      } else {
        const {production} = this.actionForm
        if (!production.selectedRecipe?.recipeId) {
          this.productionRecipeError = true
          return false
        }
        if (!this.validateInputAndOutputParameters(production)) {
          return false
        }
        return true
      }
    },
    validateInputAndOutputParameters (obj) {
      const params = ['inputParameters', 'outputParameters']
      const isValid = params.every((param) => {
        if (obj?.[param]?.mandatory?.some(variable => !this.validateDescription(variable))) {
          return false
        }
        if (obj?.[param]?.optional?.some(variable => !this.validateDescription(variable))) {
          return false
        }
        return true
      })
      return isValid
    },
    validateDescription (variable) {
      if (variable?.description?.length < 2) {
        return false
      }
      return true
    },
    isCharacterCountValidated (stage) {
      console.log(stage, "stage")
      const { actionLabel, actionDescription } = this.actionForm
      let paramObject = {}
      if (stage === ACTION_ENV_TABS.STAGING.value) {
        paramObject = this.actionForm.staging
      } else {
        paramObject = this.actionForm.production
      }
      const inputParameters = paramObject.inputParameters
      const outputParameters = paramObject.outputParameters
      const totalCount =
        (actionLabel?.length || 0) +
        (actionDescription?.length || 0) +
        (inputParameters?.mandatory?.reduce((acc, curr) => acc + curr.name.length, 0) || 0) +
        (inputParameters?.optional?.reduce((acc, curr) => acc + curr.name.length + 4, 0) || 0) +
        (outputParameters?.mandatory?.reduce((acc, curr) => acc + curr.name.length + curr.description.length, 0) || 0) +
        (outputParameters?.optional?.reduce((acc, curr) => acc + curr.name.length + curr.description.length, 0) || 0)
      return totalCount <= this.MAX_CHARACTER_LIMIT
    },
    isRecipeStatusInactive (stage) {
      if (stage === ACTION_ENV_TABS.STAGING.value) {
        const recipe = this.actionForm.staging.selectedRecipe
        if (!recipe.isActive) {
          this.stagingRecipeStatusError = true
          return true
        }
      } else {
        const recipe = this.actionForm.production.selectedRecipe
        if (!recipe.isActive) {
          this.productionRecipeStatusError = true
          return true
        }
      }
      return false
    },
    initResizeObserver () {
      this.resizeObserver = new ResizeObserver(() => {
        this.debouncedHandleWidgetWidth()
      })
      this.resizeObserver.observe(this.$refs.widgetContainer)
    },
    getLabel (field) {
      return field.isSelected ? field.name : field.name
    },
    isFieldNotPresent (list) {
      return list.length === 1 && list[0].type === 'Default'
    },
    checkForBoolean (group) {
      return group.type === 'Boolean (Max 2 values can be defined)'
    },
    addCustomField () {
      if (this.isReadOnly) return
      const newCustomField = {
        id: null,
        selectedField: null,
        value: '',
        values: []
      }
      this.actionForm[this.selectedTab].customFields.push(newCustomField)
    },
    removeCustomField (index) {
      if (this.isReadOnly) return
      const removedField = this.actionForm[this.selectedTab].customFields[index]
      if (removedField.selectedField) {
        // Mark the field as not selected in the options
        this.updateCustomFieldSelection(removedField.selectedField.id, false)
      }
      this.actionForm[this.selectedTab].customFields.splice(index, 1)
    },
    handleCustomFieldChange (selectedField, index) {
      const currentField = this.actionForm[this.selectedTab].customFields[index]

      // Mark previous field as not selected
      if (currentField.selectedField) {
        this.updateCustomFieldSelection(currentField.selectedField.id, false)
      }

      // Mark new field as selected
      if (selectedField) {
        this.updateCustomFieldSelection(selectedField.id, true)
      }

      // Update the field data
      this.$set(this.actionForm[this.selectedTab].customFields, index, {
        id: selectedField?.id || null,
        selectedField,
        value: selectedField?.type === 'TEXT' ? '' : (selectedField?.type === 'BOOLEAN' ? '' : ''),
        values: selectedField?.type === 'MULTI-SELECT' ? [] : []
      })
    },
    updateCustomFieldSelection (fieldId, isSelected) {
      if (!this.options) return
      this.options.forEach(group => {
        group.customfields.forEach(field => {
          if (field.id === fieldId) {
            this.$set(field, 'isSelected', isSelected)
          }
        })
      })
    },
    getFieldTypeLabel (type) {
      switch (type) {
        case CustomFieldNodeType.MULTISELECT:
          return 'Multi-select (multiple values can be defined)'
        case CustomFieldNodeType.BOOLEAN:
          return 'Boolean (max two value can be defined)'
        case CustomFieldNodeType.TEXT:
          return 'Text (input free text)'
        default:
          return ''
      }
    },
    transformCustomFieldsFromApi (apiCustomFields) {
      if (!apiCustomFields || !Array.isArray(apiCustomFields)) return []

      return apiCustomFields.map(apiField => {
        // Find the corresponding field definition from available custom fields
        const fieldDefinition = this.customFields.find(cf => cf.id === apiField.id)

        const selectedField = fieldDefinition || {
          id: apiField.id,
          name: apiField.name,
          type: apiField.type,
          values: apiField.values || [],
          active: apiField.active !== undefined ? apiField.active : true
        }

        // Extract the actual field values based on type
        let fieldValue = ''
        let fieldValues = []

        if (apiField.type === 'TEXT') {
          fieldValue = Array.isArray(apiField.values) && apiField.values.length > 0 ? apiField.values[0] : ''
        } else if (apiField.type === 'BOOLEAN') {
          fieldValue = Array.isArray(apiField.values) && apiField.values.length > 0 ? apiField.values[0] : ''
        } else if (apiField.type === 'MULTI-SELECT') {
          fieldValues = Array.isArray(apiField.values) ? apiField.values : []
        }

        return {
          id: apiField.id,
          selectedField,
          value: fieldValue,
          values: fieldValues
        }
      })
    },
    markSelectedCustomFields (customFields) {
      customFields.forEach(field => {
        if (field.selectedField && field.selectedField.id) {
          this.updateCustomFieldSelection(field.selectedField.id, true)
        }
      })
    }
  },
  async created () {
    this.validationRules = ACTION_FORM_VALIDATION_RULES
    this.actionsPolicy = actionsPolicy
    this.selectedTab = 'staging'
    if (!this.action) return
    this.selectedParamAccordian = ['inputParameterTitle', 'outputParameterTitle', 'customFieldParameterTitle'] // opening both accordians by providing the names array
    this.syncActionFromApi()
    this.debouncedDraftSave = debounce(this.queueSave, 1000)
    this.debouncedHandleWidgetWidth = debounce(this.handleWidgetWidth, 200)
    if (!this.$store.state.settings?.customFields) {
      await this.$store.dispatch('settings/fetchCustomFields')
    }
    this.createGroups()
  },
  mounted () {
    this.debouncedHandleWidgetWidth()
    window.addEventListener('resize', this.debouncedHandleWidgetWidth)
    this.initResizeObserver()
  },
  destroyed () {
    this.needsSave = false
    window.removeEventListener('resize', this.debouncedHandleWidgetWidth)
    if (this.observer) {
      this.observer.disconnect()
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
  },
  computed: {
    isReadOnly () {
      return (this.selectedState === ACTION_ENV_TABS.STAGING.value || this.selectedState === ACTION_ENV_TABS.PRODUCTION.value || this.selectedState === ACTION_ENV_TABS.PREVIOUS_VERSION.value || this.isActionArchived || !this.$userHasAccess(actionsPolicy._resource, actionsPolicy.UpdateAction))
    },
    canShowWidget () {
      return (this.selectedState === ACTION_ENV_TABS.STAGING.value || this.selectedState === ACTION_ENV_TABS.PRODUCTION.value) && !this.isActionArchived && this.$userHasAccess(actionsPolicy._resource, actionsPolicy.ViewWidgetAction)
    },
    isActionDraftPresent () {
      return this.actionVersions.find(version => version.stage === ACTION_ENV_TABS.DRAFT.value)?.actionId
    },
    inputParameterTitle () {
      return this.generateParameterTitle('inputParameters', 'Input parameters')
    },
    outputParameterTitle () {
      return this.generateParameterTitle('outputParameters', 'Output parameters')
    },

    isStagingSyncEnabled () {
      const stagingObj = this.actionVersions?.find(version => version.stage === ACTION_ENV_TABS.STAGING.value)
      const draftObj = this.actionVersions?.find(version => version.stage === ACTION_ENV_TABS.DRAFT.value)
      if (this.loading || !draftObj) {
        return false
      }
      if (!stagingObj) {
        return true
      }
      const compareKeys = ['actionLabel', 'actionDescription', 'sandboxActionMetadata', 'liveActionMetadata']
      const stagingComparableObject = objectPick(stagingObj, compareKeys)
      const draftComparableObject = objectPick(draftObj, compareKeys)
      if (isValuesEqual(draftComparableObject, stagingComparableObject)) {
        return false
      }
      return true
    },

    isProductionSyncDisabled () {
      const productionObj = this.actionVersions?.find(version => version.stage === ACTION_ENV_TABS.PRODUCTION.value)
      const stagingObj = this.actionVersions?.find(version => version.stage === ACTION_ENV_TABS.STAGING.value)
      if (this.saving || this.loading || !stagingObj) {
        return false
      }
      if (!productionObj) {
        return true
      }
      const compareKeys = ['actionLabel', 'actionDescription', 'sandboxActionMetadata', 'liveActionMetadata']
      const stagingComparableObject = objectPick(stagingObj, compareKeys)
      const productionComparableObject = objectPick(productionObj, compareKeys)
      if (isValuesEqual(productionComparableObject, stagingComparableObject)) {
        return false
      }
      return true
    },
    currentProductionVersion () {
      return this.actionVersions.find(version => version.stage === ACTION_ENV_TABS.PRODUCTION.value)
    },
    getActionDescription () {
      return (stage) => {
        const descriptions = {
          [ACTION_ENV_TABS.STAGING.value]: ACTION_MESSAGES.STAGING_MOVE,
          [ACTION_ENV_TABS.PRODUCTION.value]: this.productionDescription
        }
        return descriptions[stage] || ''
      }
    },
    productionDescription () {
      if (!this.currentProductionVersion) {
        return ACTION_MESSAGES.PRODUCTION_FIRST_SYNC(this.action.version)
      }
      const { version: currentVersion, isActive } = this.currentProductionVersion
      const newVersion = this.action.version
      return ACTION_MESSAGES.PRODUCTION_REPLACE(currentVersion, newVersion, isActive)
    },
    isActiveDraftState () {
      return this.selectedState === ACTION_ENV_TABS.DRAFT.value
    },
    showRecipeError () {
      if (this.selectedTab === recipeEnvTabs.STAGING.value) {
        return this.stagingRecipeError && !this.actionForm.staging.recipeId
      } else {
        return this.productionRecipeError && !this.actionForm.production.recipeId
      }
    },
    currentTabCharacterCount () {
      const { actionLabel, actionDescription } = this.actionForm
      const inputParameters = this.actionForm[this.selectedTab]?.inputParameters
      const outputParameters = this.actionForm[this.selectedTab]?.outputParameters
      const totalCount =
        (actionLabel?.length || 0) +
        (actionDescription?.length || 0) +
        (inputParameters?.mandatory?.reduce((acc, curr) => acc + curr.name.length, 0) || 0) +
        (inputParameters?.optional?.reduce((acc, curr) => acc + curr.name.length + 4, 0) || 0) +
        (outputParameters?.mandatory?.reduce((acc, curr) => acc + curr.name.length + curr.description.length, 0) || 0) +
        (outputParameters?.optional?.reduce((acc, curr) => acc + curr.name.length + curr.description.length, 0) || 0)
      return totalCount
    },
    selectedFieldName: {
      get: function () {
        return this.selectedValue?.name || ''
      },
      set: function (value) {
        console.log('value   ', value)
        // const newLinkNode = cloneDeep(value)
        // const searchNode =
        //   this.customFields &&
        //   this.customFields.find((field) => field.id === value.id)
        // newLinkNode.possibleValues = searchNode?.values || []
        // newLinkNode.values = []
        // this.handleChange(newLinkNode)
        this.createGroups()
        this.selectedValue = value
      }
    },
    dispalyOptions () {
      if (this.searchValue.trim() === '') {
        return this.options
      } else {
        const newValue = this.searchValue?.toLowerCase().trim()
        const filteredOptions = this.options
          ?.map((group) => ({
            type: group.type,
            customfields: group.customfields.filter((field) =>
              this.getLabel(field).toLowerCase().includes(newValue)
            )
          }))
          ?.filter((group) => group.customfields.length > 0)
        return filteredOptions?.length > 0
          ? filteredOptions
          : [
              {
                type: 'Default',
                customfields: [{ id: 'Default', name: 'No matching data' }]
              }
            ]
      }
    },
    selectedFieldType () {
      switch (this.selectedValue?.type) {
        case CustomFieldNodeType.MULTISELECT:
          return 'Multi Select (Multiple values can be defined)'
        case CustomFieldNodeType.BOOLEAN:
          return 'Boolean (Max 2 values can be defined)'
        case CustomFieldNodeType.TEXT:
          return 'Text (Input free text)'
        default:
          return ''
      }
    },
    customFields () {
      return this.$store.state.settings?.customFields || []
    }
    // set: function (value) {
    //   const newLinkNode = cloneDeep(this.customFieldNode)
    //   newLinkNode.type = value
    //   this.handleChange(newLinkNode)
    // }
    // }
  },

  watch: {
    customFields: {
      handler () {
        this.createGroups()
      }
    },
    actionForm: {
      handler (newVal) {
        const isFormDirty = !isValuesEqual(JSON.stringify(newVal), JSON.stringify(this.initialFormState))
        if (isFormDirty && this.isActiveDraftState && !this.loading && !this.syncing) {
          this.needsSave = true
          if (!this.debouncedDraftSave) {
            this.debouncedDraftSave = debounce(this.queueSave, 1000)
          }
          this.debouncedDraftSave()
        }
      },
      deep: true
    },
    selectedState: {
      handler (_newVal, oldVal) {
        if (oldVal) {
          this.initialFormState = {}
          this.resetActionForm()
          this.toggleIsWidgetVisible(false)
        }
      }
    },
    isWidgetVisible: {
      handler (newVal) {
        if (newVal) {
          const iframe = document.querySelector('#netomiChatWindow')
          this.observer = new MutationObserver(function () {
            const chatWindowContainer = iframe?.contentDocument?.body?.querySelector('.chat-window-container')
            if (chatWindowContainer) {
              chatWindowContainer.style.borderRadius = 0
              chatWindowContainer.style.marginBottom = 0
              iframe.style.visibility = 'visible'
            }
          })
          this.observer.observe(iframe.contentDocument.body, {
            childList: true,
            subtree: true
          })
        } else {
          if (this.observer) {
            this.observer.disconnect()
          }
        }
      }
    },
    'actionForm.staging': {
      handler (newVal) {
        if (this.syncStagingActive && this.isActiveDraftState) {
          this.actionForm.production = deepClone(newVal)
        }
      },
      deep: true
    },
    'actionForm.production': {
      handler (newVal) {
        if (this.syncStagingActive && this.isActiveDraftState && !isValuesEqual(this.actionForm.staging, newVal)) {
          this.syncStagingActive = false
          this.$store.dispatch('info', {message: 'Unchecked auto-sync checkbox from Staging.'}, { root: true })
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
  .create-action-form {
    flex: 1;
    overflow: auto;
    height: 100%;
    border: 1px solid var(--secondary-border-color);
    border-bottom: none;
    border-left: none;

    .required-field {
      color:#ff0000;
      margin-left: 4px;
    }
  }
  .test-widget-container {
    position: sticky;
    right: 0;
    top: 0;
  }
  .action-form {
    height: fit-content;
    border-right: 1px solid var(--secondary-border-color);
  }
  ::v-deep .el-collapse-item__header {
    padding:24px 12px;
  }
  ::v-deep .widget-template-loader {
    right: 50% !important;
    height: 20px !important;
    width: 20px !important;
    margin-top: 50%;
  }

  .static-chat-widget {
    position: relative;
    margin-left: 20px;

    ::v-deep .widget-template-header {
      width: 0px;
    }

    ::v-deep #netomiChatWindow {
      visibility: hidden;
      height: 100% !important;
      max-height: none !important;
      min-width: 100% !important;
      width: 100% !important;
      right: 10px !important;
    }
  }

  ::v-deep .radio-btn-cta {
    .el-radio-group {
      display: flex;
      :first-child .el-radio {
        margin-right:0px;
        .el-radio__label {
          border-radius: 4px 0 0 4px;
        }
      }
      :last-child .el-radio {
        .el-radio__label {
          border-radius: 0 4px 4px 0;
        }
      }
    }
    .el-radio__inner {
      display:none
    }
    .el-radio .el-radio__label {
      border: 1px solid var(--primary-color);
      padding: 6px 16px ;
      color: var(--secondary-color);
      font-size:16px;
      font-weight:400;
    }
    .el-radio__input.is-checked + .el-radio__label {
      background-color: var(--bg-color);
      color: var(--primary-color);
    }
    .el-radio__input.is-disabled + .el-radio__label {
      background-color: var(--color-white);
      color: var(--color-grey-steel-tint-30);
      border-color: var(--color-grey-steel-tint-30);
    }
  }
  .recipe-container {
    border: 1px solid #cdcdcd;
    border-radius: 4px;
    background-color: var(--color-white);
    padding: 16px;
    margin-bottom: 10px;
  }
  .recipe-container-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    align-items: center;
    padding: 0px 16px 0px 16px;

    & p {
      color: var(--color-black);
      font-weight: 600;
    }
  }
  .recipe-selection-box {
    padding: 0px 16px 16px 16px;

    .recipe-error {
      background: #FCF2F2;
      border-color: #F09393;
    }
  }
  ::v-deep .is-error {
    .el-textarea__inner,
    .el-input__inner {
      border-color: var(--error-border-color);

      &:hover,
      &:focus {
        border-color: var(--error-border-color);
      }

      &::placeholder {
        color: var(--dim-gray);
      }
    }

    .el-form-item__error {
      word-break: break-word;
      width: 85%;
    }
  }

  .sticky-footer {
    width: 100%;
    bottom: 0px;
    background: var(--color-white);
    border-top: 1px solid #e0e0e0;
    padding: 16px 24px;
    height: 90px;
  }

  .user-details:hover {
    cursor: pointer;
    .username {
      color: var(--secondary-color);
      font-weight: 600;
      text-decoration: underline;
    }
  }
  .username {
    font-size: 16px;
    color: var(--color-black);
  }

  .el-button--tabs {
    border: none;
    border-bottom: 1px solid #cdcdcd;
    border-radius: 0;
    color: var(--color-black);
    background: transparent;
    font-size: 16px;
    font-weight: 400;
    transition: transform 0.3s;
    padding: 11px 12px;
    &:hover,
    &.active {
      border-bottom: 2px solid var(--secondary-color);
      background: var(--color-white);
    }
  }

  ::v-deep .el-select input {
    padding: 8px 16px;
    font-family: 'proxima-nova';
  }

  ::v-deep .el-input__inner {
    border-color: var(--button-border-hover);
  }
  ::v-deep .el-button-group {
    display: flex;
    width: 492px;
    align-items: flex-start;
  }
  ::v-deep .customInput textarea {
    &::placeholder {
      color: var(--dim-gray)
    }
  }
  .custom-border {
    ::v-deep .el-button {
      font-weight: 400;
      border-color:  var(--button-border-hover);
    }
  }
  ::v-deep .el-collapse-item__header {
    background: var(--table-header-bg);
    .icon-container {
      background: var(--color-grey-steel-tint-30);
      border-radius: 12px;
    }
  }
  ::v-deep .el-collapse-item__content {
    background: var(--table-header-bg);
    border-top: 1px solid var(--secondary-color);
    padding: 0px 18px 0px 24px;
  }

  ::v-deep .el-form-item__label {
    color: var(--color-black);
    font-weight: 500;
  }

  ::v-deep .el-tabs__active-bar {
    background: var(--secondary-color);
  }

  ::v-deep .el-tabs__nav-wrap::after {
    width: 0;
  }
  ::v-deep .el-tabs__item {
    color: var(--color-black);
    font-weight: 500;
    line-height:normal;
    font-size:14px;
  }

  .parameter {
    border: 1px solid var(--color-grey-steel-tint-30);
    padding: 16px;
    margin-bottom: 8px;
    border-radius: 8px;

    .parameter__label {
      border: 1px solid #cdcdcd;
      background: var(--bg-hover-color);;
      font-size: 14px;
      line-height: 18px;
      color: var(--color-black);
      border-radius: 4px;
    }
  }

  p {
    margin-bottom: 0;
  }

  ::v-deep .el-select__input {
    padding-left: 0;
  }

  ::v-deep .el-button {
    & span {
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }
  }

  ::v-deep .el-drawer__header {
    color: var(--color-black);
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
  }

  ::v-deep .el-drawer__body {
    padding-bottom: 150px;
  }

  ::v-deep .el-card {
    border-bottom: none;
  }
  .close-btn {
    right: 16px;
    cursor: pointer;
  }
  ::v-deep .sync-dialog {
    margin-top: 30vh !important;
  }
  .custom-field-banner {
    margin-top: 32px;
    margin-bottom: 32px;
  }

  .field-container {
    display: flex;
    gap: 16px;
    margin-bottom: 32px;

    &--title {
      color: #000;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }

    &--subtitle {
      color:   #6D6D6D;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      margin-top: 4px;
    }

    &--add-button {
      border-radius: var(--Corner-Radius-S, 4px);
      background: var(--Surface-Brand-Default, #1C1F2A);
      display: flex;
      padding: var(--Spacing-XS, 8px) var(--Corner-Radius-M, 8px);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      cursor: pointer;
    }
  }
  .field {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
      gap: 8px;
  }
  .w-full {
    width: 100%;
  }
  .custom-field-block-selected {
    background: var(--color-primary-light)    !important;
  }

  .no-match-found {
    pointer-events: none;
    justify-content: center;
    color: #6d6d6d !important;
  }

  .custom-field-item {
    position: relative;
    border: 1px solid #b0b0b0;
    border-radius: 4px;
    padding: 24px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .close-button {
    position: absolute;
    top: 12px;
    right: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
  }

  .field-label {
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    color: #000000;
    margin-bottom: 8px;
  }

  .selectedFieldType {
    color: #6d6d6d;
    font-size: 14px;
    margin-top: 4px;
  }

  .separator {
    height: 1px;
    background-color: #e4e7ed;
    margin: 10px 16px;
  }
</style>
