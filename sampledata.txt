 filename: custom-field-block.vue <template> <div class="custom-field-container"> <div class="custom-field-block"> <div class="close" @click="handleClose()"> <BaseIcon name="close" width="14" height="14" class="display-block" /> </div> <div class="fields-block"> <div class="field"> <div style="display: inline-block;"> Field name </div> <el-select ref="customSelect" v-model="selectedFieldName" default-first-option placeholder="Select field" size="large" class="w-100" value-key="id" > <el-input v-model="searchValue" class="p-16" placeholder="Search" prefix-icon="el-icon-search" /> <el-option-group v-for="group in dispalyOptions" :key="group.type" :label="getGroupLabel(group.type)" > <el-option v-for="field in group.customfields" :key="field.id" :label="getLabel(field)" :value="field" class="options" :disabled="field.isSelected" :class="{

'no-match-found': isFieldNotPresent(dispalyOptions),

'custom-field-block-selected': field.isSelected

}" data-search-exclude="true" /> <div v-if="

!isFieldNotPresent(dispalyOptions) && checkForBoolean(group)

" class="separator" /> </el-option-group> </el-select> <div v-if="selectedFieldType" class="selectedFieldType"> Field Type: {{ selectedFieldType }} </div> </div> <div v-if="selectedFieldType" class="field"> Field value <dropdown v-if="isFieldTypeText" :data="dropdownData" :values="selectedFieldValues" :onValuesChange="

(value) => {

selectedFieldValues = value

}

" /> <template v-else> <CustomFieldBlockDropDown :possibleFieldValues="possibleFieldValues" :values="selectedFieldValues" :onValuesChange="

(value) => {

selectedFieldValues = value

}

" /> </template> </div> </div> </div> </div> </template> filename: custom-field-block.vue <script> import { CustomFieldNodeType } from '@/constants' import { cloneDeep } from 'lodash' import BaseIcon from 'src/components/global-components/BaseIcon.vue' import CustomFieldBlockDropDown from './custom-field-drop-down.vue' import Dropdown from './dropdown' export default { components: { Dropdown, BaseIcon, CustomFieldBlockDropDown }, props: { customFieldNode: Object, customFields: Array, customFieldIndex: Number, dropdownData: Array }, data () { return { options: [], searchValue: '', observer: null, filteredOptions: [] } }, computed: { dispalyOptions () { if (this.searchValue.trim() === '') { return this.options } else { const newValue = this.searchValue?.toLowerCase().trim() const filteredOptions = this.options ?.map((group) => ({ type: group.type, customfields: group.customfields.filter((field) => this.getLabel(field).toLowerCase().includes(newValue) ) })) ?.filter((group) => group.customfields.length > 0) return filteredOptions?.length > 0 ? filteredOptions : [ { type: 'Default', customfields: [{ id: 'Default', name: 'No matching data' }] } ] } }, selectedFieldName: { get: function () { return this.customFieldNode?.name || '' }, set: function (value) { const newLinkNode = cloneDeep(value) const searchNode = this.customFields && this.customFields.find((field) => field.id === value.id) newLinkNode.possibleValues = searchNode?.values || [] newLinkNode.values = [] this.handleChange(newLinkNode) this.createGroups() } }, selectedFieldType: { get: function () { switch (this.customFieldNode?.type) { case CustomFieldNodeType.MULTISELECT: return 'Multi Select (Multiple values can be defined)' case CustomFieldNodeType.BOOLEAN: return 'Boolean (Max 2 values can be defined)' case CustomFieldNodeType.TEXT: return 'Text (Input free text)' default: return '' } }, set: function (value) { const newLinkNode = cloneDeep(this.customFieldNode) newLinkNode.type = value this.handleChange(newLinkNode) } }, possibleFieldValues () { return this.customFieldNode?.possibleValues || [] }, isFieldTypeText () { return this.customFieldNode?.type === 'TEXT' }, selectedFieldValues: { get: function () { return this.customFieldNode?.values || [] }, set: function (values) { const newLinkNode = cloneDeep(this.customFieldNode) newLinkNode.values = values this.handleChange(newLinkNode) } } }, watch: { customFields: { handler () { this.createGroups() } } }, created () { this.createGroups() }, mounted () { this.updateTagsTextElements() this.observer = new MutationObserver(this.updateTagsTextElements.bind(this)) this.observer.observe(document.body, { subtree: true, childList: true }) }, beforeDestroy () { if (this.observer) { this.observer.disconnect() } }, methods: { checkForBoolean (group) { return group.type === 'Boolean (Max 2 values can be defined)' }, preventDropdown (event) { event.stopPropagation() }, updateTagsTextElements () { this.tagsTextElements = Array.from( document.querySelectorAll('.el-select__tags-text') ) this.tagsTextElements.forEach((element) => { element.addEventListener('click', this.preventDropdown) }) }, isFieldNotPresent (list) { return list.length === 1 && list[0].type === 'Default' }, createGroups () { const fields = cloneDeep(this.customFields) fields.sort((a, b) => { const typeOrder = { TEXT: 1, 'MULTI-SELECT': 2, BOOLEAN: 3 } return typeOrder[a.type] - typeOrder[b.type] }) const groupedOptions = {} fields.forEach((option) => { if (!groupedOptions[option.type]) { groupedOptions[option.type] = { type: option.type, customfields: [] } } groupedOptions[option.type].customfields.push(option) }) this.options = Object.values(groupedOptions) }, handleChange (newCustomFieldNode) { this.$emit('updateCustomFieldNode', { newCustomFieldNode, index: this.customFieldIndex }) }, handleClose () { this.$emit('close', this.customFieldNode) }, getLabel (field) { return field.isSelected ? field.name : field.name }, getGroupLabel (type) { switch (type) { case CustomFieldNodeType.MULTISELECT: return 'Multi Select (Multiple values can be defined)' case CustomFieldNodeType.BOOLEAN: return 'Boolean (Max 2 values can be defined)' case CustomFieldNodeType.TEXT: return 'Text (Input free text)' default: return '' } } } } </script> <style lang="scss" scoped> .custom-field-block-outer-container { display: flex; flex-direction: column; } .custom-field-container { display: flex; width: 100%; .close { display: flex; padding: 12px 8px 12px 16px; justify-content: flex-end; align-items: center; gap: 24px; align-self: flex-end; cursor: pointer; } .fields-block { width: 100%; display: flex; padding: 0px 24px 24px 24px; flex-direction: column; align-items: flex-start; gap: 24px; .field { display: flex; flex-direction: column; align-items: flex-start; width: 100%; gap: 8px; } } .custom-field-block { display: flex; width: 100%; flex-direction: column; align-items: flex-start; border-radius: 4px; border: 1px solid #b0b0b0; } } .custom-field-block-selected { background: var(--color-primary-light) !important; } .separator { height: 1px; /* Adjust the height to control the thickness */ background-color: #e4e7ed; margin: 10px 16px 10px 16px; } .no-match-found { pointer-events: none; justify-content: center; color: #6d6d6d !important; } .el-select-dropdown__item.selected { color: #454849 !important; font-weight: 400; /* Removing font-weight declaration */ } .selectedFieldType { color: #6d6d6d; } ::v-deep .el-select__tags-text { overflow: hidden; word-wrap: break-word; user-select: text; } .options { display: flex; align-items: center; gap: 12px; text-transform: capitalize; .icon { width: 24px; aspect-ratio: 1; border-radius: 50%; display: flex; align-items: center; justify-content: center; } .label { font-weight: 400; font-size: 16px; line-height: 20px; color: #000000; } } ::v-deep .el-select__caret { color: var(--color-primary) !important; font-weight: bolder; } ::v-deep .el-select-dropdown { margin-top: 2px; height: 398px; } ::v-deep .el-select-dropdown__wrap { max-height: 398px !important; } ::v-deep .el-select-group__title { font-size: 14px; font-style: normal; font-weight: 600; line-height: 18px; padding: 10px 16px 10px 16px; position: relative; } ::v-deep .el-select-dropdown__item { padding: 10px 16px 12px 16px; font-size: 16px !important; font-style: normal; font-weight: 400; line-height: 20px; } .group-icon { position: absolute; left: 15px; top: 2px; } ul.el-select-group__wrap:not(:last-of-type)::after { display: none !important; height: 0 !important; } .el-select-group__wrap:not(:last-of-type) { padding: 0; } ::v-deep .el-input__prefix { left: 20px; } </style> ==================== // filename custom-field-drop-down.vue <template> <el-select :disabled="disabled" :allow-create="allowCreate" popper-class="custom-field-popper-class" filterable :value="values" default-first-option multiple placeholder="Select values" size="large" class="w-100" @change="onValuesChange" > <el-option v-for="fieldValue in possibleFieldValues" :key="fieldValue" :label="fieldValue" :value="fieldValue" class="options" data-search-exclude="true" /> </el-select> </template> <script> export default { props: { disabled: { type: Boolean, default: false }, allowCreate: { type: Boolean, default: false }, possibleFieldValues: { type: Array, default: function () { return [] } }, values: { type: Array, default: function () { return [] } }, onValuesChange: Function } } </script> <style lang="scss" scoped> .options { display: flex; align-items: center; gap: 12px; padding: 8px 16px; .icon { width: 24px; aspect-ratio: 1; border-radius: 50%; display: flex; align-items: center; justify-content: center; } .label { font-weight: 400; font-size: 16px; line-height: 20px; color: #000000; } } ::v-deep .el-select__caret { color: var(--color-primary) !important; font-weight: bolder; } ::v-deep .el-icon-arrow-up:before { color: var(--color-primary) !important; font-weight: bolder; } ::v-deep .el-select-dropdown__item.selected { color: #454849 !important; font-weight: 400; } ::v-deep .popper__arrow { display: none; } ::v-deep .el-select__tags-text { overflow: hidden; word-wrap: break-word; user-select: text; } ::v-deep .el-tag { padding: 5px 8px 5px 16px; background: var(--color-primary-light); border: 1px solid var(--color-primary); border-radius: 24px; height: auto; color: var(--color-primary); font-weight: 400; font-size: 16px; line-height: 20px; white-space: unset; .el-icon-close { color: var(--color-primary) !important; background-color: transparent !important; font-weight: bolder; transform: scale(1.2); right: -3px; } } ::v-deep .el-select-group__title { font-size: 14px; font-style: normal; font-weight: 600; line-height: 18px; padding: 6px 20px 6px 46px; position: relative; border-bottom: 1px solid #e4e7ed; } ::v-deep .el-select-dropdown__item { padding: 6px 20px 6px 46px; font-size: 16px !important; font-style: normal; font-weight: 400; line-height: 20px; margin-bottom: 6px; } .group-icon { position: absolute; left: 15px; top: 2px; } ul.el-select-group__wrap:not(:last-of-type)::after { display: none !important; height: 0 !important; } .el-select-group__wrap { margin: 16px 0px !important; } </style> =================================== filename: dropdown.vue <!-- TODO NF-36053: Move this to global components as this is used at multiple places and has updated design with icons --> <template> <el-select ref="customSelect" filterable :value="values" default-first-option :allow-create="allowCreate" multiple style="width: 100%;" :placeholder="placeholder" size="large" class="entity-variable-select" :multiple-limit="multipleLimit" @change="onValuesChange" > <el-option-group v-for="group in options" :key="group.type" :label="group.type" class="select-dropdown" > <span v-if="iconMap[group.type]" class="group-icon"> <BaseIcon :name="iconMap[group.type]" width="24" height="24" /> </span> <el-option v-for="item in group.options" :key="item.viewId" :label="getValue(item)" :value="item.value" class="options" data-search-exclude="true" /> </el-option-group> </el-select> </template> <script> import BaseIcon from 'src/components/global-components/BaseIcon.vue' import ReferenceVariables, { ReferenceVariableKeys } from '../models/ReferenceVariables' const iconMap = { 'API integration variables': 'CableEntities', 'System variables': 'SettingsApplication', 'Pre-trained entities': 'ConversionPath', 'Build integration entities': 'IntegrationBuilderEntities', 'Sub workflow variables': 'RebaseEntities', 'Global variables': 'Category', 'Dictionary entities': 'ImportContacts', 'Regex entities': 'RegularExpression' } export default { components: { BaseIcon }, props: { data: { type: Array, default: function () { return {} } }, values: { type: Array, default: function () { return [] } }, onValuesChange: Function, placeholder: { type: String, default: 'Enter text / select entity / select variable' }, allowCreate: { type: Boolean, default: true }, multipleLimit: { type: Number, default: 0 } }, data () { return { options: [], searchText: '' } }, watch: { data: { handler () { this.createGroups() }, deep: true } }, created () { this.createGroups() this.iconMap = iconMap }, methods: { createGroups: function () { const valueList = [] const categorizedOptions = { DICTIONARY: [], REGEX: [] } const groupedOptions = this.data.reduce((acc, curr) => { const group = ReferenceVariables.LabelKeyMapper[curr.group] if (curr.dsVariableEntityType) { const label = this.getValue(curr); const value = this.setValueForVariables(curr) if (label) { categorizedOptions[curr.dsVariableEntityType].push({ label, name: curr.name, value }) valueList.append(value) } } else { if (!acc[group]) { acc[group] = [] } const label = this.getValue(curr); const value = this.setValueForVariables(curr) if (label) { acc[group].push({ label, name: curr.name, value }) valueList.append(value) } } return acc }, {}) this.options = Object.keys(groupedOptions).reduce((acc, curr) => { acc.push({ type: curr, options: groupedOptions[curr] }) return acc }, []) if (categorizedOptions.DICTIONARY.length) { this.options.push({ type: 'Dictionary entities', options: categorizedOptions.DICTIONARY }) } if (categorizedOptions.REGEX.length) { this.options.push({ type: 'Regex entities', options: categorizedOptions.REGEX }) } const updatedValues = this.values.filter((value) => !value.startsWith('${') || valueList.includes(value)) if (this.values.length !== updatedValues.length) { this.values = updatedValues this.onValuesChange(this.values) } }, getValue: function (item) { return item.label || item.name }, setValueForVariables (curr) { if (curr?.variableId) { return '${' + curr.name + '|' + curr.variableCode + '|' + curr.variableId + '}' } else if (curr.group === ReferenceVariableKeys.VAR_LOCAL) { return '${' + curr.name + '|' + ReferenceVariables.UIBackendKeyMapper[ReferenceVariableKeys.VAR_PROMPT] + '}' } else { return '${' + curr.name + '|' + ReferenceVariables.UIBackendKeyMapper[curr.group] + '}' } } } } </script> <style scoped lang="scss"> @import 'src/assets/styles/dropdown.scss'; </style> ================================================= // filename : index.vue <template> <Inspector :formErrors="formErrors" :cell="cell" :handleClose="handleClose" :beta="false" > <div id="response-action"> <div class="condition-node"> <div> <div class="title"> Fields </div> <div class="note"> Configure custom fields that will be added to the conversation as tags in your agent desk. </div> </div> <el-tooltip slot="suffix" placement="top" :open-delay="1000"> <div slot="content"> Add </div> <IconButton type="primary" class="add-button" @click="addCustomFieldNode"> <BaseIcon name="Add" /> </IconButton> </el-tooltip> </div> <InlineBanner variant="info" title="Manage response fields in Settings > AI Agent." class="my-32" /> <div class="custom-fields-settings" > <div class="custom-field-block"> <div class="message-block"> <div> <el-form> <el-form-item v-for="(customFieldNode, index) in customFieldNodes" :key="index" > <CustomFieldBlock :customFieldIndex="index" :customFieldNode="customFieldNode" :customFields="customFields" :dropdownData="dropdownData" @updateCustomFieldNode="onNodeChange" @close="deleteNode" /> </el-form-item> </el-form> </div> </div> </div> </div> </div> </Inspector> </template> <script lang="ts"> import { cloneDeep } from 'lodash' import BaseIcon from 'src/components/global-components/BaseIcon.vue' import CircularIcon from 'src/components/global-components/CircularIcon.vue' import IconButton from 'src/components/global-components/IconButton.vue' import InlineBanner from 'src/components/global-components/InlineBanner.vue' import { BaseInspector } from 'src/components/inspector/base-inspector/base-inspector' import Inspector from 'src/components/inspector/components/Inspector.vue' import { Icons, Node } from 'src/constants' import { setSelection } from 'src/rappid/actions' import { alphaSortCaseInsensitive } from 'src/services/utils' import Vue from 'vue' import Component from 'vue-class-component' import { InjectReactive, Prop, Watch } from 'vue-property-decorator' import { Entities } from '../../../classes' import CustomFieldBlock from './custom-field-block.vue' @Component({ components: { CustomFieldBlock, CircularIcon, Inspector, IconButton, BaseIcon, InlineBanner } }) export default class CustomField extends BaseInspector { @Prop() isEmailWf: boolean @InjectReactive('formErrors') formErrors!: any public label = 'Custom Field' public customFieldNodes = [] public customFields = [] public newDesign = false public initObject = { id: '', botId: '', name: '', type: '', values: [], customId: '', isActive: true } public props = { label: ['attrs', 'label', 'text'], customFields: ['customFields'], position: ['position'] } public dropdownData = [] get showAddCustomFieldButton () { return this.customFieldNodes.length >= this.customFields.length } get iconName () { return Icons[Node.CUSTOM_FIELD].name } get iconColor () { return Icons[Node.CUSTOM_FIELD].color } created () { this.fetchRequiredData() } @Watch('cell.id') fetchRequiredData () { this.dropdownData = new Entities(this.cell.graph, { cellId: this.cell.id }).getAutocompleteDropdownData() } public mounted (): void { this.updateCustomFields(Vue.prototype.$global.customFields) this.updateCustomFieldNodes( this.customFieldNodes, Vue.prototype.$global.customFields ) } updateCustomFieldNodes (customFieldsNodes, customFields) { this.customFieldNodes = customFieldsNodes.map((node) => { const searchNode = customFields && customFields.find((field) => field.id === node.id) node.possibleValues = searchNode?.values || [] return node }) } public handleClose () { setSelection(this.rappid, []) } updateCustomFields (data) { const unSortedFIelds = this.formatCustomFields(data) this.customFields = unSortedFIelds?.sort(alphaSortCaseInsensitive('name')) || [] } formatCustomFields (customFields) { if (!customFields || !customFields.length) return [] return customFields.map((customFieldObj) => { return { ...customFieldObj, isSelected: !!this.customFieldNodes.find( (node) => node.id === customFieldObj.id ) } }) } public onNodeChange (data: any): void { const customFieldNodes = cloneDeep(this.customFieldNodes) // Enable selection for old Node this.updateSelection(customFieldNodes[data.index], false) // Disable selection for new Node this.updateSelection(data.newCustomFieldNode, true) // Update CustomFieldsNodes State customFieldNodes[data.index] = data.newCustomFieldNode this.customFieldNodes = customFieldNodes this.changeCellProp( this.props.customFields, Object.assign([], customFieldNodes) ) } public updateSelection (node, selection) { if (!node || !node.id) return this.customFields = this.customFields.map((customFieldObj) => node.id === customFieldObj.id ? { ...customFieldObj, isSelected: selection } : customFieldObj ) } public deleteNode (customFieldNode: any): void { this.updateSelection(customFieldNode, false) const customFieldNodes = this.customFieldNodes.filter( (node) => node.id !== customFieldNode.id ) this.customFieldNodes = customFieldNodes this.changeCellProp( this.props.customFields, Object.assign([], customFieldNodes) ) } public addCustomFieldNode () { if (!this.showAddCustomFieldButton) { this.customFieldNodes.push(this.initObject) } } protected assignFormFields (): void { const { cell, props } = this this.label = cell.prop(props.label) const customFields = Vue.prototype.$global.customFields || [] this.updateCustomFieldNodes( cell.prop(props.customFields) || [this.initObject], customFields ) this.updateCustomFields(customFields) } } </script> <style lang="scss" scoped> #response-action { display: flex; flex-direction: column; height: 100%; .action-heading { font-size: 14px; font-family: 'proxima-nova', sans-serif !important; font-weight: normal; font-stretch: normal; font-style: normal; line-height: 1.07; letter-spacing: normal; color: #afafaf; margin: 0 0 5px; padding: 0; } .border-block { width: 100%; height: auto; flex: 1; flex-direction: column; justify-content: flex-start; align-items: center; border-radius: 2px; border: solid 0.5px #d9d9d9; background-color: #ffffff; padding: 0; box-sizing: border-box; .action-head { display: flex; align-items: center; gap: 10px; } .custom-field-description-container { padding: 15px 20px 0 20px; .info-text { font-size: 13px; color: #b0afaf; margin: 0; } } .main-area { width: 100%; padding: 0; margin: 0; height: 100%; box-sizing: border-box; flex: 1; overflow: auto; } .welcome-msg { color: #303030; font-size: 16px; font-weight: 400; text-align: center; margin-bottom: 20px; font-family: 'proxima-nova', sans-serif !important; } img { max-width: 100%; } } .condition-container { display: flex; flex: 1; padding: 10px 20px; flex-direction: column; .condition-border-block { display: flex; flex-direction: column; margin-bottom: 20px; } } .condition-block-inner-container { display: flex; flex-direction: row; justify-content: center; align-items: center; } .condition-header { font-size: 13px; color: #b0afaf; } } .custom-fields-settings { margin: 0; .custom-field-block { .block-head { font-size: 16px; font-weight: 600; line-height: normal; color: #000; margin: 0 0 24px 0; } .message { display: inline-flex; .el-input-inner { color: var(--color-primary); } } } } .swf-inspector-container { border: 1.5px solid rgb(191, 209, 240); border-radius: 3px; background-color: rgb(237, 243, 253); margin-bottom: 25px; display: flex; gap: 10px; padding: 5px; } .swf-inspector-container-icon { margin-top: 3px; margin-left: 5px; } .container { gap: 16px; } ::v-deep .el-form-item__content { font-size: 14px; line-height: normal !important; } ::v-deep .is-error { .el-form-item__error { word-break: break-word; width: 85%; margin-left: 16px; } } ::v-deep .el-collapse-item__header.is-active .active-icon-color path { fill: var(--color-primary); } .swf-inspector-container-text { font-family: 'proxima-nova', sans-serif; font-weight: 400; font-size: 13px; line-height: 20px; } .condition-node { display: flex; align-items: flex-start; gap: 16px; margin-top: 32px; .title { font-weight: 600; font-size: 16px; line-height: 20px; color: #000000; } .note { font-weight: 400; font-size: 14px; line-height: 18px; color: #6d6d6d; margin-top: 4px; } .add-button { flex: 1; aspect-ratio: 1; } } .link-label-input > ::v-deep .el-input__inner { -webkit-appearance: none; background-color: transparent; background-image: none; border-radius: 0; border: 0; box-sizing: border-box; color: #606266; display: inline-block; font-size: inherit; height: 30px; line-height: 30px; font-weight: 400; outline: 0; padding: 0; width: 100%; } </style>
